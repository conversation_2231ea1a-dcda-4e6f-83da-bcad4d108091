# TradingAgents Frontend 快速开始指南

## 📋 前置要求

在开始之前，请确保您的系统已安装以下软件：

- **Node.js** 18.0 或更高版本
- **npm** 或 **yarn** 包管理器
- **Git** 版本控制工具

## 🚀 快速启动

### 1. 克隆项目

```bash
git clone <repository-url>
cd trading-agents-frontend
```

### 2. 自动化设置（推荐）

我们提供了自动化设置脚本，可以一键完成环境配置：

```bash
# 给脚本执行权限
chmod +x scripts/setup.sh

# 运行设置脚本
./scripts/setup.sh
```

脚本会自动：
- 检查 Node.js 版本
- 安装项目依赖
- 创建环境配置文件
- 运行代码检查
- 启动开发服务器

### 3. 手动设置

如果您偏好手动设置，请按以下步骤操作：

#### 3.1 安装依赖

```bash
npm install
# 或
yarn install
```

#### 3.2 环境配置

复制环境变量示例文件：

```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件，配置必要的环境变量：

```env
# API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# OpenAI API配置
NEXT_PUBLIC_OPENAI_API_KEY=sk-yOXwTRVHIub4m6WjEWin68sqvdYypExLyBbChOc38SX4PnpW
NEXT_PUBLIC_OPENAI_BASE_URL=https://api.nuwaapi.com

# FinnHub API配置
NEXT_PUBLIC_FINNHUB_API_KEY=d1htbc1r01qhsrhc1180d1htbc1r01qhsrhc118g

# 应用配置
NEXT_PUBLIC_APP_NAME=TradingAgents
NEXT_PUBLIC_APP_VERSION=1.0.0
```

#### 3.3 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

应用将在 [http://localhost:3000](http://localhost:3000) 启动。

## 🔧 开发命令

```bash
# 开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm run start

# 代码检查
npm run lint

# TypeScript 类型检查
npm run type-check

# 修复代码格式
npm run lint:fix
```

## 🐳 Docker 部署

### 开发环境

```bash
# 构建镜像
docker build -f docker/Dockerfile -t tradingagents-frontend .

# 运行容器
docker run -p 3000:3000 tradingagents-frontend
```

### 生产环境

使用 Docker Compose 部署完整的应用栈：

```bash
# 启动所有服务
docker-compose -f docker/docker-compose.yml up -d

# 查看服务状态
docker-compose -f docker/docker-compose.yml ps

# 查看日志
docker-compose -f docker/docker-compose.yml logs -f frontend

# 停止服务
docker-compose -f docker/docker-compose.yml down
```

## 📁 项目结构说明

```
trading-agents-frontend/
├── src/                    # 源代码目录
│   ├── app/               # Next.js App Router
│   ├── components/        # React 组件
│   ├── hooks/            # 自定义 Hooks
│   ├── lib/              # 工具库和配置
│   ├── store/            # 状态管理
│   ├── types/            # TypeScript 类型定义
│   └── utils/            # 工具函数
├── public/               # 静态资源
├── docker/              # Docker 配置
├── scripts/             # 脚本文件
└── .github/             # GitHub Actions
```

## 🎯 核心功能

### 1. 欢迎页面
- 项目介绍和功能展示
- 分析配置表单
- 参数验证和提交

### 2. 交易仪表板
- **总览**: 分析进度和配置信息
- **代理状态**: 实时监控各代理工作状态
- **实时数据**: 股价、技术指标、新闻、基本面数据
- **分析报告**: 各代理生成的详细报告
- **交易决策**: 最终的交易建议和参数

### 3. 实时通信
- WebSocket 连接实时更新
- 自动重连机制
- 连接状态监控

## 🔌 后端集成

确保 TradingAgents 后端服务正在运行：

```bash
# 检查后端健康状态
curl http://localhost:8000/api/health

# 检查 WebSocket 连接
# 使用浏览器开发者工具或 WebSocket 客户端测试
```

## 🎨 自定义配置

### 主题配置

编辑 `tailwind.config.js` 自定义主题：

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          // 自定义主色调
        },
      },
    },
  },
};
```

### API 配置

编辑 `src/lib/api.ts` 自定义 API 配置：

```typescript
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
  timeout: 30000,
  retryAttempts: 3,
};
```

## 🧪 测试

```bash
# 运行单元测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行 E2E 测试
npm run test:e2e
```

## 📊 性能优化

### 构建优化

```bash
# 分析构建包大小
npm run analyze

# 生成构建报告
npm run build:analyze
```

### 监控和调试

- 使用浏览器开发者工具监控性能
- 检查 Network 面板的 API 请求
- 使用 React DevTools 调试组件状态

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 更改端口
   PORT=3001 npm run dev
   ```

2. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **TypeScript 错误**
   ```bash
   # 重新生成类型定义
   npm run type-check
   ```

4. **WebSocket 连接失败**
   - 检查后端服务是否运行
   - 确认 WebSocket URL 配置正确
   - 检查防火墙设置

### 调试模式

启用详细日志：

```bash
DEBUG=* npm run dev
```

## 📞 获取帮助

- **GitHub Issues**: 报告 Bug 或请求功能
- **Discord 社区**: 实时技术交流
- **文档**: 查看详细的 API 文档

## 🎉 下一步

现在您已经成功启动了 TradingAgents Frontend！

1. 访问 [http://localhost:3000](http://localhost:3000)
2. 配置您的第一个分析任务
3. 探索各种功能和界面
4. 查看实时数据和分析报告

祝您使用愉快！🚀
